package com.stepup.service;

import com.stepup.dto.project.CreateFeatureReqDTO;
import com.stepup.dto.project.FeatureResDTO;
import com.stepup.model.project.Feature;
import com.stepup.repository.FeatureRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class FeatureService extends CommonService {
    private final FeatureRepository featureRepository;

    @Autowired
    public FeatureService(FeatureRepository featureRepository) {
        this.featureRepository = featureRepository;
    }

    public FeatureResDTO createFeature(String projectId) {
        Feature feature = Feature.builder()
                .name("New Feature")
                .description("Auto-generated feature")
                .projectId(projectId)
                .build();

        Feature savedFeature = featureRepository.save(feature);
        return mapToFeatureResponse(savedFeature);
    }

    public FeatureResDTO createFeature(String projectId, CreateFeatureReqDTO request) {
        Feature feature = Feature.builder()
                .name(request.getName())
                .description(request.getDescription())
                .projectId(projectId)
                .build();

        Feature savedFeature = featureRepository.save(feature);
        return mapToFeatureResponse(savedFeature);
    }

    public List<FeatureResDTO> getFeaturesByProjectId(String projectId) {
        return featureRepository.findByProjectIdAndIsActiveTrue(projectId)
                .stream()
                .map(this::mapToFeatureResponse)
                .collect(Collectors.toList());
    }

    public List<FeatureResDTO> getAllFeaturesByProjectId(String projectId) {
        return featureRepository.findByProjectId(projectId)
                .stream()
                .map(this::mapToFeatureResponse)
                .collect(Collectors.toList());
    }

    public List<FeatureResDTO> getRecentFeatures() {
        return featureRepository.findTop2ByOrderByLastAccessDateDesc()
                .stream()
                .limit(2)
                .map(this::mapToFeatureResponse)
                .collect(Collectors.toList());
    }

    public FeatureResDTO updateFeature(String featureId, CreateFeatureReqDTO request) {
        Optional<Feature> featureOpt = featureRepository.findById(featureId);
        if (featureOpt.isEmpty()) {
            throw new RuntimeException("Feature not found with id: " + featureId);
        }

        Feature feature = featureOpt.get();
        feature.setName(request.getName());
        feature.setDescription(request.getDescription());

        Feature savedFeature = featureRepository.save(feature);
        return mapToFeatureResponse(savedFeature);
    }

    public void deleteFeature(String featureId) {
        if (!featureRepository.existsById(featureId)) {
            throw new RuntimeException("Feature not found with id: " + featureId);
        }
        featureRepository.deleteById(featureId);
    }

    public String getFeatureTestCases(String featureId) {
        return featureRepository.findFeatureDataByFeatureId(featureId);
    }

    private FeatureResDTO mapToFeatureResponse(Feature feature) {
        return FeatureResDTO.builder()
                .id(feature.getId())
                .name(feature.getName())
                .description(feature.getDescription())
                .projectId(feature.getProjectId())
                .status(feature.getStatus())
                .isActive(feature.getIsActive())
                .lastAccessDate(feature.getLastAccessDate())
                .build();
    }
} 