package com.stepup.model.test;

import com.stepup.model.ModifierTrackingEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "test_cases", indexes = {
        @Index(name = "idx_checklist_item_id", columnList = "checklist_item_id"),
        @Index(name = "tc_idx_status", columnList = "status"),
        @Index(name = "tc_idx_priority", columnList = "priority")
})
public class TestCase extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(50)")
    private String id;

    @Column(name = "feature_id")
    private String featureId;

    @Column(name = "checklist_item_id")
    private String checklistItemId;

    @Column(name = "data", columnDefinition = "TEXT")
    private String data;

    @NotNull
    @Column(name = "priority", nullable = false)
    @Builder.Default
    private Integer priority = 1;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    @Builder.Default
    private TestCaseStatus status = TestCaseStatus.PENDING;

    @Column(name = "note")
    private Integer note;

    @Column(name = "assigned_to")
    private String assignedTo;

    public enum TestCaseStatus {
        PENDING, IN_PROGRESS, PASSED, FAILED, BLOCKED, SKIPPED
    }
}