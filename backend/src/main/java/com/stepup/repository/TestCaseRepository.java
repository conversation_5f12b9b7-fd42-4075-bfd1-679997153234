package com.stepup.repository;

import com.stepup.model.test.TestCase;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TestCaseRepository extends JpaRepository<TestCase, String> {
    
    /**
     * Find test cases by checklist item id
     */
    List<TestCase> findByChecklistItemId(String checklistItemId);
    
    /**
     * Find test cases by status
     */
    List<TestCase> findByStatus(TestCase.TestCaseStatus status);
    
    /**
     * Find test cases by priority
     */
    List<TestCase> findByPriority(Integer priority);
    
    /**
     * Check if test case exists for checklist item
     */
    boolean existsByChecklistItemId(String checklistItemId);
}